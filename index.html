<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Orchestration Automation</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <button class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="header-title">
                    <h1>Payment Orchestration Automation</h1>
                    <span class="run-count">30 runs</span>
                </div>
            </div>
            <div class="header-right">
                <button class="btn btn-secondary">Start</button>
                <div class="btn-group">
                    <button class="btn btn-primary active">Build</button>
                    <button class="btn btn-secondary">Test</button>
                </div>
                <div class="version-selector">
                    <select>
                        <option>Version 2</option>
                        <option>Version 1</option>
                    </select>
                </div>
                <button class="menu-btn">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Workflow Canvas -->
            <div class="workflow-canvas" id="canvas">
                <!-- Start Node -->
                <div class="workflow-node start-node" style="top: 200px; left: 60px;">
                    <div class="node-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">TRIGGER ACTION</div>
                        <div class="node-title">Start Automation</div>
                    </div>
                </div>

                <!-- Conditions Node -->
                <div class="workflow-node condition-node" style="top: 200px; left: 200px;">
                    <div class="node-icon">
                        <i class="fas fa-code-branch"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">CONDITIONS</div>
                        <div class="node-title">2 conditions</div>
                    </div>
                </div>

                <!-- Currency Condition -->
                <div class="workflow-node small-condition" style="top: 140px; left: 320px;">
                    <div class="condition-text">Currency = USD</div>
                </div>

                <!-- Country Condition -->
                <div class="workflow-node small-condition green" style="top: 260px; left: 320px;">
                    <div class="condition-text">Country = GBR</div>
                </div>

                <!-- Stripe Authorize -->
                <div class="workflow-node action-node" style="top: 80px; left: 450px;">
                    <div class="node-icon stripe">
                        <span>S</span>
                    </div>
                    <div class="node-content">
                        <div class="node-label">STRIPE ACTION</div>
                        <div class="node-title">Authorise</div>
                    </div>
                </div>

                <!-- Adyen Authorize -->
                <div class="workflow-node action-node" style="top: 200px; left: 450px;">
                    <div class="node-icon adyen">
                        <span>A</span>
                    </div>
                    <div class="node-content">
                        <div class="node-label">ADYEN ACTION</div>
                        <div class="node-title">Authorise</div>
                    </div>
                </div>

                <!-- Payment Condition Node -->
                <div class="workflow-node condition-node" style="top: 300px; left: 320px;">
                    <div class="node-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">PAYMENT CONDITION</div>
                        <div class="node-title">50/50</div>
                    </div>
                </div>

                <!-- Braintree Authorize -->
                <div class="workflow-node action-node" style="top: 400px; left: 450px;">
                    <div class="node-icon braintree">
                        <span>B</span>
                    </div>
                    <div class="node-content">
                        <div class="node-label">BRAINTREE ACTION</div>
                        <div class="node-title">Authorise</div>
                    </div>
                </div>

                <!-- Right side conditions and actions -->
                <div class="workflow-node action-node failed" style="top: 200px; left: 680px;">
                    <div class="node-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">PAYMENT ACTION</div>
                        <div class="node-title">Authorise</div>
                    </div>
                </div>

                <div class="workflow-node small-condition" style="top: 260px; left: 600px;">
                    <div class="condition-text">Status = Failed</div>
                </div>

                <div class="workflow-node condition-node" style="top: 300px; left: 580px;">
                    <div class="node-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">PAYMENT CONDITION</div>
                        <div class="node-title">2 conditions</div>
                    </div>
                </div>

                <div class="workflow-node small-condition green" style="top: 360px; left: 620px;">
                    <div class="condition-text">Status = Authorised</div>
                </div>

                <div class="workflow-node action-node success" style="top: 400px; left: 720px;">
                    <div class="node-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">PAYMENT ACTION</div>
                        <div class="node-title">Capture</div>
                    </div>
                </div>

                <!-- Connection lines will be drawn by JavaScript -->
                <svg class="connections-svg" id="connectionsSvg">
                    <!-- SVG paths for connections will be added here -->
                </svg>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <i class="fas fa-chevron-up"></i>
                    <span>No-code building blocks</span>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
