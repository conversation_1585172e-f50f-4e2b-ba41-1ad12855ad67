<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Query Builder</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <button class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="header-title">
                    <h1>SQL Query Builder</h1>
                    <span class="run-count">Database Operations</span>
                </div>
            </div>
            <div class="header-right">
                <button class="btn btn-success" id="createQueryBtn">Create Query</button>
                <div class="btn-group">
                    <button class="btn btn-primary active">Build</button>
                    <button class="btn btn-secondary">Preview</button>
                </div>
                <button class="btn btn-secondary" id="clearBtn">Clear All</button>
                <button class="menu-btn">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Workflow Canvas -->
            <div class="workflow-canvas" id="canvas">
                <!-- SELECT Node -->
                <div class="workflow-node select-node" data-type="SELECT" style="top: 100px; left: 60px;">
                    <div class="node-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">SELECT</div>
                        <div class="node-title">Choose Columns</div>
                        <input type="text" class="node-input" placeholder="*, column1, column2" value="*">
                    </div>
                </div>

                <!-- FROM Node -->
                <div class="workflow-node from-node" data-type="FROM" style="top: 200px; left: 60px;">
                    <div class="node-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">FROM</div>
                        <div class="node-title">Table Source</div>
                        <input type="text" class="node-input" placeholder="table_name" value="">
                    </div>
                </div>

                <!-- WHERE Node -->
                <div class="workflow-node where-node" data-type="WHERE" style="top: 300px; left: 60px;">
                    <div class="node-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">WHERE</div>
                        <div class="node-title">Filter Conditions</div>
                        <input type="text" class="node-input" placeholder="column = value" value="">
                    </div>
                </div>

                <!-- JOIN Node -->
                <div class="workflow-node join-node" data-type="JOIN" style="top: 100px; left: 300px;">
                    <div class="node-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">JOIN</div>
                        <div class="node-title">Table Join</div>
                        <select class="node-select">
                            <option value="INNER">INNER JOIN</option>
                            <option value="LEFT">LEFT JOIN</option>
                            <option value="RIGHT">RIGHT JOIN</option>
                            <option value="FULL">FULL JOIN</option>
                        </select>
                        <input type="text" class="node-input" placeholder="table2 ON condition" value="">
                    </div>
                </div>

                <!-- ORDER BY Node -->
                <div class="workflow-node order-node" data-type="ORDER" style="top: 200px; left: 300px;">
                    <div class="node-icon">
                        <i class="fas fa-sort"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">ORDER BY</div>
                        <div class="node-title">Sort Results</div>
                        <input type="text" class="node-input" placeholder="column ASC/DESC" value="">
                    </div>
                </div>

                <!-- GROUP BY Node -->
                <div class="workflow-node group-node" data-type="GROUP" style="top: 300px; left: 300px;">
                    <div class="node-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">GROUP BY</div>
                        <div class="node-title">Group Results</div>
                        <input type="text" class="node-input" placeholder="column1, column2" value="">
                    </div>
                </div>

                <!-- HAVING Node -->
                <div class="workflow-node having-node" data-type="HAVING" style="top: 400px; left: 300px;">
                    <div class="node-icon">
                        <i class="fas fa-funnel-dollar"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">HAVING</div>
                        <div class="node-title">Group Filter</div>
                        <input type="text" class="node-input" placeholder="COUNT(*) > 5" value="">
                    </div>
                </div>

                <!-- LIMIT Node -->
                <div class="workflow-node limit-node" data-type="LIMIT" style="top: 100px; left: 540px;">
                    <div class="node-icon">
                        <i class="fas fa-stop"></i>
                    </div>
                    <div class="node-content">
                        <div class="node-label">LIMIT</div>
                        <div class="node-title">Row Limit</div>
                        <input type="number" class="node-input" placeholder="100" value="">
                    </div>
                </div>

                <!-- Connection lines will be drawn by JavaScript -->
                <svg class="connections-svg" id="connectionsSvg">
                    <!-- SVG paths for connections will be added here -->
                </svg>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <i class="fas fa-code"></i>
                    <span>SQL Query Preview</span>
                </div>
                <div class="query-preview">
                    <div class="query-output" id="queryOutput">
                        <div class="placeholder-text">
                            Connect your SQL nodes and click "Create Query" to see the generated SQL
                        </div>
                    </div>
                    <div class="query-actions">
                        <button class="btn btn-sm btn-secondary" id="copyQueryBtn">
                            <i class="fas fa-copy"></i> Copy Query
                        </button>
                        <button class="btn btn-sm btn-primary" id="formatQueryBtn">
                            <i class="fas fa-magic"></i> Format
                        </button>
                    </div>
                </div>

                <div class="node-palette">
                    <div class="palette-header">
                        <i class="fas fa-plus"></i>
                        <span>Add SQL Nodes</span>
                    </div>
                    <div class="palette-nodes">
                        <button class="palette-node" data-node-type="SELECT">
                            <i class="fas fa-search"></i> SELECT
                        </button>
                        <button class="palette-node" data-node-type="FROM">
                            <i class="fas fa-table"></i> FROM
                        </button>
                        <button class="palette-node" data-node-type="WHERE">
                            <i class="fas fa-filter"></i> WHERE
                        </button>
                        <button class="palette-node" data-node-type="JOIN">
                            <i class="fas fa-link"></i> JOIN
                        </button>
                        <button class="palette-node" data-node-type="ORDER">
                            <i class="fas fa-sort"></i> ORDER BY
                        </button>
                        <button class="palette-node" data-node-type="GROUP">
                            <i class="fas fa-layer-group"></i> GROUP BY
                        </button>
                        <button class="palette-node" data-node-type="HAVING">
                            <i class="fas fa-funnel-dollar"></i> HAVING
                        </button>
                        <button class="palette-node" data-node-type="LIMIT">
                            <i class="fas fa-stop"></i> LIMIT
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
