<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Query Builder</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <button class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="header-title">
                    <h1>SQL Query Builder</h1>
                    <span class="run-count">Database Operations</span>
                </div>
            </div>
            <div class="header-right">
                <button class="btn btn-success" id="createQueryBtn">Create Query</button>
                <div class="btn-group">
                    <button class="btn btn-primary active">Build</button>
                    <button class="btn btn-secondary">Preview</button>
                </div>
                <button class="btn btn-secondary" id="clearBtn">Clear All</button>
                <button class="menu-btn">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Workflow Canvas -->
            <div class="workflow-canvas" id="canvas">
                <!-- Empty canvas - nodes will be added dynamically -->
                <div class="empty-canvas-message" id="emptyMessage">
                    <div class="empty-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>SQL Query Builder</h3>
                    <p>Press <kbd>Ctrl+A</kbd> to add SQL nodes or use the palette on the right</p>
                    <p>Click and drag between node ports to create connections</p>
                </div>

                <!-- Connection lines will be drawn by JavaScript -->
                <svg class="connections-svg" id="connectionsSvg">
                    <!-- SVG paths for connections will be added here -->
                </svg>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <i class="fas fa-code"></i>
                    <span>SQL Query Preview</span>
                </div>
                <div class="query-preview">
                    <div class="query-output" id="queryOutput">
                        <div class="placeholder-text">
                            Connect your SQL nodes and click "Create Query" to see the generated SQL
                        </div>
                    </div>
                    <div class="query-actions">
                        <button class="btn btn-sm btn-secondary" id="copyQueryBtn">
                            <i class="fas fa-copy"></i> Copy Query
                        </button>
                        <button class="btn btn-sm btn-primary" id="formatQueryBtn">
                            <i class="fas fa-magic"></i> Format
                        </button>
                    </div>
                </div>

                <div class="node-palette">
                    <div class="palette-header">
                        <i class="fas fa-plus"></i>
                        <span>Add SQL Nodes</span>
                    </div>
                    <div class="palette-nodes">
                        <button class="palette-node" data-node-type="SELECT">
                            <i class="fas fa-search"></i> SELECT
                        </button>
                        <button class="palette-node" data-node-type="FROM">
                            <i class="fas fa-table"></i> FROM
                        </button>
                        <button class="palette-node" data-node-type="WHERE">
                            <i class="fas fa-filter"></i> WHERE
                        </button>
                        <button class="palette-node" data-node-type="JOIN">
                            <i class="fas fa-link"></i> JOIN
                        </button>
                        <button class="palette-node" data-node-type="ORDER">
                            <i class="fas fa-sort"></i> ORDER BY
                        </button>
                        <button class="palette-node" data-node-type="GROUP">
                            <i class="fas fa-layer-group"></i> GROUP BY
                        </button>
                        <button class="palette-node" data-node-type="HAVING">
                            <i class="fas fa-funnel-dollar"></i> HAVING
                        </button>
                        <button class="palette-node" data-node-type="LIMIT">
                            <i class="fas fa-stop"></i> LIMIT
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Node Selection Modal (Ctrl+A) -->
    <div class="node-modal" id="nodeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add SQL Node</h3>
                <span class="modal-close" id="modalClose">&times;</span>
            </div>
            <div class="modal-body">
                <div class="node-grid">
                    <div class="modal-node" data-node-type="SELECT">
                        <i class="fas fa-search"></i>
                        <span>SELECT</span>
                        <small>Choose columns</small>
                    </div>
                    <div class="modal-node" data-node-type="FROM">
                        <i class="fas fa-table"></i>
                        <span>FROM</span>
                        <small>Table source</small>
                    </div>
                    <div class="modal-node" data-node-type="WHERE">
                        <i class="fas fa-filter"></i>
                        <span>WHERE</span>
                        <small>Filter conditions</small>
                    </div>
                    <div class="modal-node" data-node-type="JOIN">
                        <i class="fas fa-link"></i>
                        <span>JOIN</span>
                        <small>Table join</small>
                    </div>
                    <div class="modal-node" data-node-type="ORDER">
                        <i class="fas fa-sort"></i>
                        <span>ORDER BY</span>
                        <small>Sort results</small>
                    </div>
                    <div class="modal-node" data-node-type="GROUP">
                        <i class="fas fa-layer-group"></i>
                        <span>GROUP BY</span>
                        <small>Group results</small>
                    </div>
                    <div class="modal-node" data-node-type="HAVING">
                        <i class="fas fa-funnel-dollar"></i>
                        <span>HAVING</span>
                        <small>Group filter</small>
                    </div>
                    <div class="modal-node" data-node-type="LIMIT">
                        <i class="fas fa-stop"></i>
                        <span>LIMIT</span>
                        <small>Row limit</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
