* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: #2d2d2d;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #404040;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.back-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
}

.header-title h1 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.run-count {
    font-size: 12px;
    color: #888;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #404040;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-group {
    display: flex;
    border-radius: 6px;
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid #555;
}

.btn-group .btn:last-child {
    border-right: none;
}

.btn.active {
    background: #28a745;
}

.version-selector select {
    background: #404040;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
}

.menu-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    position: relative;
}

.workflow-canvas {
    flex: 1;
    background: #1a1a1a;
    background-image:
        radial-gradient(circle, #333 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    overflow: hidden;
}

/* Empty Canvas Message */
.empty-canvas-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
    pointer-events: none;
    z-index: 1;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #444;
}

.empty-canvas-message h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #888;
}

.empty-canvas-message p {
    margin: 8px 0;
    font-size: 14px;
}

.empty-canvas-message kbd {
    background: #333;
    border: 1px solid #555;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    color: #fff;
}

/* Workflow Nodes */
.workflow-node {
    position: absolute;
    background: #2d2d2d;
    border: 2px solid #404040;
    border-radius: 8px;
    padding: 12px;
    min-width: 140px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
    user-select: none;
}

.workflow-node:hover {
    border-color: #007bff;
    transform: translateY(-2px);
}

.workflow-node.selected {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
    transform: translateY(-2px);
}

/* Connection Ports */
.workflow-node::before,
.workflow-node::after {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background: #666;
    border: 2px solid #888;
    border-radius: 50%;
    transition: all 0.2s;
}

.workflow-node::before {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    background: #007bff; /* Input port */
}

.workflow-node::after {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    background: #28a745; /* Output port */
}

.workflow-node:hover::before,
.workflow-node:hover::after {
    transform: translateY(-50%) scale(1.2);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.workflow-node.selected::before,
.workflow-node.selected::after {
    border-color: #fff;
}

/* SQL Node Styles */
.select-node {
    border-color: #007bff;
    background: #1e2a3a;
}

.from-node {
    border-color: #28a745;
    background: #1e3a1e;
}

.where-node {
    border-color: #ffc107;
    background: #3a3a1e;
}

.join-node {
    border-color: #6f42c1;
    background: #2a1e3a;
}

.order-node {
    border-color: #17a2b8;
    background: #1e3a3a;
}

.group-node {
    border-color: #fd7e14;
    background: #3a2a1e;
}

.having-node {
    border-color: #e83e8c;
    background: #3a1e2a;
}

.limit-node {
    border-color: #dc3545;
    background: #3a1e1e;
}

.small-condition {
    min-width: 100px;
    padding: 8px 12px;
    font-size: 12px;
    background: #404040;
    border-color: #666;
}

.small-condition.green {
    border-color: #28a745;
    background: #1e3a1e;
}

.node-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.start-node .node-icon {
    background: #28a745;
}

.condition-node .node-icon {
    background: #17a2b8;
}

.action-node .node-icon {
    background: #6f42c1;
}

.action-node.failed .node-icon {
    background: #dc3545;
}

.action-node.success .node-icon {
    background: #28a745;
}

.node-icon.stripe {
    background: #635bff;
    color: white;
    font-weight: bold;
}

.node-icon.adyen {
    background: #0abf53;
    color: white;
    font-weight: bold;
}

.node-icon.braintree {
    background: #009cde;
    color: white;
    font-weight: bold;
}

.node-content {
    flex: 1;
}

.node-label {
    font-size: 10px;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.node-title {
    font-size: 14px;
    font-weight: 500;
}

.condition-text {
    font-size: 12px;
    text-align: center;
}

/* Node Input Styles */
.node-input, .node-select {
    width: 100%;
    background: #404040;
    border: 1px solid #666;
    border-radius: 4px;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    margin-top: 4px;
}

.node-input:focus, .node-select:focus {
    outline: none;
    border-color: #007bff;
    background: #4a4a4a;
}

.node-input::placeholder {
    color: #888;
}

/* Connection Lines */
.connections-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.workflow-node {
    z-index: 2;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #2d2d2d;
    border-left: 1px solid #404040;
    padding: 20px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #888;
    cursor: pointer;
}

.sidebar-header:hover {
    color: #ffffff;
}

/* Query Preview Styles */
.query-preview {
    margin-bottom: 30px;
}

.query-output {
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
}

.placeholder-text {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.query-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Node Palette Styles */
.palette-header {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #888;
    margin-bottom: 15px;
    padding-top: 20px;
    border-top: 1px solid #404040;
}

.palette-nodes {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.palette-node {
    background: #404040;
    border: 1px solid #666;
    border-radius: 6px;
    color: white;
    padding: 10px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.palette-node:hover {
    background: #4a4a4a;
    border-color: #007bff;
    transform: translateY(-1px);
}

.palette-node i {
    font-size: 12px;
}

/* Node Selection Modal */
.node-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #2d2d2d;
    border-radius: 12px;
    padding: 0;
    min-width: 500px;
    max-width: 600px;
    border: 1px solid #404040;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

.modal-close {
    font-size: 24px;
    color: #888;
    cursor: pointer;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #fff;
}

.modal-body {
    padding: 25px;
}

.node-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.modal-node {
    background: #404040;
    border: 2px solid #666;
    border-radius: 8px;
    padding: 15px 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.modal-node:hover {
    border-color: #007bff;
    background: #4a4a4a;
    transform: translateY(-2px);
}

.modal-node i {
    font-size: 20px;
    color: #007bff;
}

.modal-node span {
    font-weight: 500;
    color: #fff;
    font-size: 12px;
}

.modal-node small {
    color: #888;
    font-size: 10px;
}

/* Connection Drawing */
.connection-line {
    stroke: #666;
    stroke-width: 2;
    fill: none;
    pointer-events: stroke;
    cursor: pointer;
}

.connection-line:hover {
    stroke: #007bff;
    stroke-width: 3;
}

.connection-line.selected {
    stroke: #28a745;
    stroke-width: 3;
}

.temp-connection {
    stroke: #ffc107;
    stroke-width: 2;
    stroke-dasharray: 5,5;
    fill: none;
    pointer-events: none;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .header-right {
        gap: 8px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}
