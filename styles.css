* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: #2d2d2d;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #404040;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.back-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
}

.header-title h1 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.run-count {
    font-size: 12px;
    color: #888;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-secondary {
    background: #404040;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-group {
    display: flex;
    border-radius: 6px;
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid #555;
}

.btn-group .btn:last-child {
    border-right: none;
}

.btn.active {
    background: #28a745;
}

.version-selector select {
    background: #404040;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
}

.menu-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    position: relative;
}

.workflow-canvas {
    flex: 1;
    background: #1a1a1a;
    background-image: 
        radial-gradient(circle, #333 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    overflow: hidden;
}

/* Workflow Nodes */
.workflow-node {
    position: absolute;
    background: #2d2d2d;
    border: 2px solid #404040;
    border-radius: 8px;
    padding: 12px;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
}

.workflow-node:hover {
    border-color: #007bff;
    transform: translateY(-2px);
}

/* SQL Node Styles */
.select-node {
    border-color: #007bff;
    background: #1e2a3a;
}

.from-node {
    border-color: #28a745;
    background: #1e3a1e;
}

.where-node {
    border-color: #ffc107;
    background: #3a3a1e;
}

.join-node {
    border-color: #6f42c1;
    background: #2a1e3a;
}

.order-node {
    border-color: #17a2b8;
    background: #1e3a3a;
}

.group-node {
    border-color: #fd7e14;
    background: #3a2a1e;
}

.having-node {
    border-color: #e83e8c;
    background: #3a1e2a;
}

.limit-node {
    border-color: #dc3545;
    background: #3a1e1e;
}

.small-condition {
    min-width: 100px;
    padding: 8px 12px;
    font-size: 12px;
    background: #404040;
    border-color: #666;
}

.small-condition.green {
    border-color: #28a745;
    background: #1e3a1e;
}

.node-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.start-node .node-icon {
    background: #28a745;
}

.condition-node .node-icon {
    background: #17a2b8;
}

.action-node .node-icon {
    background: #6f42c1;
}

.action-node.failed .node-icon {
    background: #dc3545;
}

.action-node.success .node-icon {
    background: #28a745;
}

.node-icon.stripe {
    background: #635bff;
    color: white;
    font-weight: bold;
}

.node-icon.adyen {
    background: #0abf53;
    color: white;
    font-weight: bold;
}

.node-icon.braintree {
    background: #009cde;
    color: white;
    font-weight: bold;
}

.node-content {
    flex: 1;
}

.node-label {
    font-size: 10px;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.node-title {
    font-size: 14px;
    font-weight: 500;
}

.condition-text {
    font-size: 12px;
    text-align: center;
}

/* Node Input Styles */
.node-input, .node-select {
    width: 100%;
    background: #404040;
    border: 1px solid #666;
    border-radius: 4px;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    margin-top: 4px;
}

.node-input:focus, .node-select:focus {
    outline: none;
    border-color: #007bff;
    background: #4a4a4a;
}

.node-input::placeholder {
    color: #888;
}

/* Connection Lines */
.connections-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.workflow-node {
    z-index: 2;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #2d2d2d;
    border-left: 1px solid #404040;
    padding: 20px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #888;
    cursor: pointer;
}

.sidebar-header:hover {
    color: #ffffff;
}

/* Query Preview Styles */
.query-preview {
    margin-bottom: 30px;
}

.query-output {
    background: #1a1a1a;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
}

.placeholder-text {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.query-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* Node Palette Styles */
.palette-header {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #888;
    margin-bottom: 15px;
    padding-top: 20px;
    border-top: 1px solid #404040;
}

.palette-nodes {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.palette-node {
    background: #404040;
    border: 1px solid #666;
    border-radius: 6px;
    color: white;
    padding: 10px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.palette-node:hover {
    background: #4a4a4a;
    border-color: #007bff;
    transform: translateY(-1px);
}

.palette-node i {
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .header-right {
        gap: 8px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}
