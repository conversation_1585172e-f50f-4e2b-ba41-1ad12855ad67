document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('canvas');
    const svg = document.getElementById('connectionsSvg');
    const queryOutput = document.getElementById('queryOutput');
    const createQueryBtn = document.getElementById('createQueryBtn');
    const clearBtn = document.getElementById('clearBtn');
    const copyQueryBtn = document.getElementById('copyQueryBtn');
    const formatQueryBtn = document.getElementById('formatQueryBtn');

    // SQL Query Builder State
    let connections = [];
    let nodeCounter = 0;

    // SQL Node Templates
    const nodeTemplates = {
        SELECT: {
            icon: 'fas fa-search',
            label: 'SELECT',
            title: 'Choose Columns',
            input: { type: 'text', placeholder: '*, column1, column2', value: '*' },
            color: '#007bff'
        },
        FROM: {
            icon: 'fas fa-table',
            label: 'FROM',
            title: 'Table Source',
            input: { type: 'text', placeholder: 'table_name', value: '' },
            color: '#28a745'
        },
        WHERE: {
            icon: 'fas fa-filter',
            label: 'WHERE',
            title: 'Filter Conditions',
            input: { type: 'text', placeholder: 'column = value', value: '' },
            color: '#ffc107'
        },
        JOIN: {
            icon: 'fas fa-link',
            label: 'JOIN',
            title: 'Table Join',
            select: {
                options: [
                    { value: 'INNER', text: 'INNER JOIN' },
                    { value: 'LEFT', text: 'LEFT JOIN' },
                    { value: 'RIGHT', text: 'RIGHT JOIN' },
                    { value: 'FULL', text: 'FULL JOIN' }
                ]
            },
            input: { type: 'text', placeholder: 'table2 ON condition', value: '' },
            color: '#6f42c1'
        },
        ORDER: {
            icon: 'fas fa-sort',
            label: 'ORDER BY',
            title: 'Sort Results',
            input: { type: 'text', placeholder: 'column ASC/DESC', value: '' },
            color: '#17a2b8'
        },
        GROUP: {
            icon: 'fas fa-layer-group',
            label: 'GROUP BY',
            title: 'Group Results',
            input: { type: 'text', placeholder: 'column1, column2', value: '' },
            color: '#fd7e14'
        },
        HAVING: {
            icon: 'fas fa-funnel-dollar',
            label: 'HAVING',
            title: 'Group Filter',
            input: { type: 'text', placeholder: 'COUNT(*) > 5', value: '' },
            color: '#e83e8c'
        },
        LIMIT: {
            icon: 'fas fa-stop',
            label: 'LIMIT',
            title: 'Row Limit',
            input: { type: 'number', placeholder: '100', value: '' },
            color: '#dc3545'
        }
    };
    
    function getNodeCenter(element) {
        const rect = element.getBoundingClientRect();
        const canvasRect = canvas.getBoundingClientRect();
        return {
            x: rect.left - canvasRect.left + rect.width / 2,
            y: rect.top - canvasRect.top + rect.height / 2
        };
    }
    
    function createPath(from, to) {
        const fromCenter = getNodeCenter(from);
        const toCenter = getNodeCenter(to);
        
        // Create a curved path
        const dx = toCenter.x - fromCenter.x;
        const dy = toCenter.y - fromCenter.y;
        
        // Control points for bezier curve
        const cp1x = fromCenter.x + dx * 0.5;
        const cp1y = fromCenter.y;
        const cp2x = toCenter.x - dx * 0.5;
        const cp2y = toCenter.y;
        
        return `M ${fromCenter.x} ${fromCenter.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toCenter.x} ${toCenter.y}`;
    }
    
    function drawConnections() {
        // Clear existing paths
        svg.innerHTML = '';

        connections.forEach(connection => {
            const fromElement = document.getElementById(connection.from);
            const toElement = document.getElementById(connection.to);

            if (fromElement && toElement) {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', createPath(fromElement, toElement));
                path.setAttribute('stroke', '#666');
                path.setAttribute('stroke-width', '2');
                path.setAttribute('stroke-dasharray', '5,5');
                path.setAttribute('fill', 'none');
                path.setAttribute('opacity', '0.7');

                svg.appendChild(path);
            }
        });
    }

    // SQL Query Generation
    function generateSQLQuery() {
        const nodes = document.querySelectorAll('.workflow-node');
        const sqlParts = {
            SELECT: [],
            FROM: [],
            JOIN: [],
            WHERE: [],
            GROUP: [],
            HAVING: [],
            ORDER: [],
            LIMIT: []
        };

        // Collect data from all nodes
        nodes.forEach(node => {
            const type = node.getAttribute('data-type');
            if (!type) return;

            const input = node.querySelector('.node-input');
            const select = node.querySelector('.node-select');

            let value = input ? input.value.trim() : '';
            let selectValue = select ? select.value : '';

            if (value || (type === 'SELECT' && !value)) {
                switch(type) {
                    case 'SELECT':
                        sqlParts.SELECT.push(value || '*');
                        break;
                    case 'FROM':
                        if (value) sqlParts.FROM.push(value);
                        break;
                    case 'WHERE':
                        if (value) sqlParts.WHERE.push(value);
                        break;
                    case 'JOIN':
                        if (value) sqlParts.JOIN.push(`${selectValue} ${value}`);
                        break;
                    case 'ORDER':
                        if (value) sqlParts.ORDER.push(value);
                        break;
                    case 'GROUP':
                        if (value) sqlParts.GROUP.push(value);
                        break;
                    case 'HAVING':
                        if (value) sqlParts.HAVING.push(value);
                        break;
                    case 'LIMIT':
                        if (value) sqlParts.LIMIT.push(value);
                        break;
                }
            }
        });

        // Build the SQL query
        let query = '';

        // SELECT clause (required)
        if (sqlParts.SELECT.length > 0) {
            query += `SELECT ${sqlParts.SELECT.join(', ')}\n`;
        } else {
            query += 'SELECT *\n';
        }

        // FROM clause
        if (sqlParts.FROM.length > 0) {
            query += `FROM ${sqlParts.FROM.join(', ')}\n`;
        }

        // JOIN clauses
        if (sqlParts.JOIN.length > 0) {
            sqlParts.JOIN.forEach(join => {
                query += `${join}\n`;
            });
        }

        // WHERE clause
        if (sqlParts.WHERE.length > 0) {
            query += `WHERE ${sqlParts.WHERE.join(' AND ')}\n`;
        }

        // GROUP BY clause
        if (sqlParts.GROUP.length > 0) {
            query += `GROUP BY ${sqlParts.GROUP.join(', ')}\n`;
        }

        // HAVING clause
        if (sqlParts.HAVING.length > 0) {
            query += `HAVING ${sqlParts.HAVING.join(' AND ')}\n`;
        }

        // ORDER BY clause
        if (sqlParts.ORDER.length > 0) {
            query += `ORDER BY ${sqlParts.ORDER.join(', ')}\n`;
        }

        // LIMIT clause
        if (sqlParts.LIMIT.length > 0) {
            query += `LIMIT ${sqlParts.LIMIT[0]}\n`;
        }

        return query.trim();
    }
    
    // Draw connections on load
    setTimeout(drawConnections, 100);
    
    // Redraw connections on window resize
    window.addEventListener('resize', drawConnections);
    
    // Add hover effects to nodes
    const nodes = document.querySelectorAll('.workflow-node');
    nodes.forEach(node => {
        node.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        
        node.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Event Handlers
    createQueryBtn.addEventListener('click', function() {
        const query = generateSQLQuery();
        displayQuery(query);
    });

    clearBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to clear all nodes?')) {
            clearAllNodes();
        }
    });

    copyQueryBtn.addEventListener('click', function() {
        const queryText = queryOutput.textContent;
        if (queryText && queryText !== 'Connect your SQL nodes and click "Create Query" to see the generated SQL') {
            navigator.clipboard.writeText(queryText).then(() => {
                this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-copy"></i> Copy Query';
                }, 2000);
            });
        }
    });

    formatQueryBtn.addEventListener('click', function() {
        const query = generateSQLQuery();
        const formattedQuery = formatSQL(query);
        displayQuery(formattedQuery);
    });

    function displayQuery(query) {
        if (query.trim()) {
            queryOutput.innerHTML = `<pre>${query}</pre>`;
        } else {
            queryOutput.innerHTML = '<div class="placeholder-text">No valid SQL query could be generated. Please check your node configurations.</div>';
        }
    }

    function clearAllNodes() {
        const nodes = document.querySelectorAll('.workflow-node');
        nodes.forEach(node => {
            const inputs = node.querySelectorAll('.node-input');
            inputs.forEach(input => input.value = '');
        });
        queryOutput.innerHTML = '<div class="placeholder-text">Connect your SQL nodes and click "Create Query" to see the generated SQL</div>';
    }

    function formatSQL(query) {
        return query
            .replace(/SELECT/g, 'SELECT')
            .replace(/FROM/g, '\nFROM')
            .replace(/WHERE/g, '\nWHERE')
            .replace(/JOIN/g, '\n  JOIN')
            .replace(/GROUP BY/g, '\nGROUP BY')
            .replace(/HAVING/g, '\nHAVING')
            .replace(/ORDER BY/g, '\nORDER BY')
            .replace(/LIMIT/g, '\nLIMIT');
    }
    
    // Add drag functionality to nodes
    let isDragging = false;
    let currentNode = null;
    let offset = { x: 0, y: 0 };
    
    nodes.forEach(node => {
        node.addEventListener('mousedown', function(e) {
            isDragging = true;
            currentNode = this;
            const rect = this.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            offset.x = e.clientX - rect.left;
            offset.y = e.clientY - rect.top;
            this.style.zIndex = '1000';
        });
    });
    
    document.addEventListener('mousemove', function(e) {
        if (isDragging && currentNode) {
            const canvasRect = canvas.getBoundingClientRect();
            const x = e.clientX - canvasRect.left - offset.x;
            const y = e.clientY - canvasRect.top - offset.y;
            
            currentNode.style.left = Math.max(0, Math.min(x, canvas.offsetWidth - currentNode.offsetWidth)) + 'px';
            currentNode.style.top = Math.max(0, Math.min(y, canvas.offsetHeight - currentNode.offsetHeight)) + 'px';
            
            // Redraw connections while dragging
            drawConnections();
        }
    });
    
    document.addEventListener('mouseup', function() {
        if (isDragging && currentNode) {
            currentNode.style.zIndex = '2';
            isDragging = false;
            currentNode = null;
        }
    });
    
    // Node Palette Functionality
    const paletteNodes = document.querySelectorAll('.palette-node');
    paletteNodes.forEach(btn => {
        btn.addEventListener('click', function() {
            const nodeType = this.getAttribute('data-node-type');
            addNewNode(nodeType);
        });
    });

    function addNewNode(type) {
        const template = nodeTemplates[type];
        if (!template) return;

        nodeCounter++;
        const nodeId = `node-${type.toLowerCase()}-${nodeCounter}`;

        // Create new node element
        const nodeDiv = document.createElement('div');
        nodeDiv.className = `workflow-node ${type.toLowerCase()}-node`;
        nodeDiv.setAttribute('data-type', type);
        nodeDiv.id = nodeId;
        nodeDiv.style.top = '50px';
        nodeDiv.style.left = '200px';
        nodeDiv.style.position = 'absolute';

        // Build node content
        let nodeHTML = `
            <div class="node-icon">
                <i class="${template.icon}"></i>
            </div>
            <div class="node-content">
                <div class="node-label">${template.label}</div>
                <div class="node-title">${template.title}</div>
        `;

        if (template.select) {
            nodeHTML += '<select class="node-select">';
            template.select.options.forEach(option => {
                nodeHTML += `<option value="${option.value}">${option.text}</option>`;
            });
            nodeHTML += '</select>';
        }

        if (template.input) {
            nodeHTML += `<input type="${template.input.type}" class="node-input" placeholder="${template.input.placeholder}" value="${template.input.value}">`;
        }

        nodeHTML += '</div>';
        nodeDiv.innerHTML = nodeHTML;

        // Add to canvas
        canvas.appendChild(nodeDiv);

        // Add drag functionality to new node
        addDragFunctionality(nodeDiv);

        // Add hover effects
        nodeDiv.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        nodeDiv.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    }

    function addDragFunctionality(node) {
        node.addEventListener('mousedown', function(e) {
            isDragging = true;
            currentNode = this;
            const rect = this.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            offset.x = e.clientX - rect.left;
            offset.y = e.clientY - rect.top;
            this.style.zIndex = '1000';
        });
    }

    // Initialize drag functionality for existing nodes
    const existingNodes = document.querySelectorAll('.workflow-node');
    existingNodes.forEach(addDragFunctionality);
});
