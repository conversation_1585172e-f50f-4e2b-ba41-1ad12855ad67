document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('canvas');
    const svg = document.getElementById('connectionsSvg');
    
    // Connection data - defines which nodes connect to which
    const connections = [
        // From Start to Conditions
        { from: '.start-node', to: '.condition-node' },
        
        // From Conditions to Currency/Country conditions
        { from: '.condition-node', to: '.small-condition:not(.green)' },
        { from: '.condition-node', to: '.small-condition.green' },
        
        // From Currency condition to Stripe
        { from: '.small-condition:not(.green)', to: '.action-node:nth-of-type(4)' },
        
        // From Country condition to Adyen
        { from: '.small-condition.green', to: '.action-node:nth-of-type(5)' },
        
        // From Country condition to Payment Condition
        { from: '.small-condition.green', to: '.condition-node:nth-of-type(6)' },
        
        // From Payment Condition to Braintree
        { from: '.condition-node:nth-of-type(6)', to: '.action-node:nth-of-type(7)' },
        
        // From Adyen to Failed node
        { from: '.action-node:nth-of-type(5)', to: '.action-node.failed' },
        
        // From Failed to Status condition
        { from: '.action-node.failed', to: '.small-condition:nth-of-type(3)' },
        
        // From Status condition to Payment Condition 2
        { from: '.small-condition:nth-of-type(3)', to: '.condition-node:nth-of-type(8)' },
        
        // From Payment Condition 2 to Authorised condition
        { from: '.condition-node:nth-of-type(8)', to: '.small-condition:nth-of-type(4)' },
        
        // From Authorised condition to Capture
        { from: '.small-condition:nth-of-type(4)', to: '.action-node.success' }
    ];
    
    function getNodeCenter(element) {
        const rect = element.getBoundingClientRect();
        const canvasRect = canvas.getBoundingClientRect();
        return {
            x: rect.left - canvasRect.left + rect.width / 2,
            y: rect.top - canvasRect.top + rect.height / 2
        };
    }
    
    function createPath(from, to) {
        const fromCenter = getNodeCenter(from);
        const toCenter = getNodeCenter(to);
        
        // Create a curved path
        const dx = toCenter.x - fromCenter.x;
        const dy = toCenter.y - fromCenter.y;
        
        // Control points for bezier curve
        const cp1x = fromCenter.x + dx * 0.5;
        const cp1y = fromCenter.y;
        const cp2x = toCenter.x - dx * 0.5;
        const cp2y = toCenter.y;
        
        return `M ${fromCenter.x} ${fromCenter.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${toCenter.x} ${toCenter.y}`;
    }
    
    function drawConnections() {
        // Clear existing paths
        svg.innerHTML = '';
        
        connections.forEach(connection => {
            const fromElement = document.querySelector(connection.from);
            const toElement = document.querySelector(connection.to);
            
            if (fromElement && toElement) {
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                path.setAttribute('d', createPath(fromElement, toElement));
                path.setAttribute('stroke', '#666');
                path.setAttribute('stroke-width', '2');
                path.setAttribute('stroke-dasharray', '5,5');
                path.setAttribute('fill', 'none');
                path.setAttribute('opacity', '0.7');
                
                svg.appendChild(path);
            }
        });
    }
    
    // Draw connections on load
    setTimeout(drawConnections, 100);
    
    // Redraw connections on window resize
    window.addEventListener('resize', drawConnections);
    
    // Add hover effects to nodes
    const nodes = document.querySelectorAll('.workflow-node');
    nodes.forEach(node => {
        node.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });
        
        node.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add click handlers for buttons
    document.querySelector('.back-btn').addEventListener('click', function() {
        console.log('Back button clicked');
    });
    
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.textContent === 'Start') {
                console.log('Starting automation...');
                this.textContent = 'Running...';
                this.style.background = '#ffc107';
                
                setTimeout(() => {
                    this.textContent = 'Start';
                    this.style.background = '#404040';
                }, 2000);
            }
        });
    });
    
    // Add drag functionality to nodes
    let isDragging = false;
    let currentNode = null;
    let offset = { x: 0, y: 0 };
    
    nodes.forEach(node => {
        node.addEventListener('mousedown', function(e) {
            isDragging = true;
            currentNode = this;
            const rect = this.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            offset.x = e.clientX - rect.left;
            offset.y = e.clientY - rect.top;
            this.style.zIndex = '1000';
        });
    });
    
    document.addEventListener('mousemove', function(e) {
        if (isDragging && currentNode) {
            const canvasRect = canvas.getBoundingClientRect();
            const x = e.clientX - canvasRect.left - offset.x;
            const y = e.clientY - canvasRect.top - offset.y;
            
            currentNode.style.left = Math.max(0, Math.min(x, canvas.offsetWidth - currentNode.offsetWidth)) + 'px';
            currentNode.style.top = Math.max(0, Math.min(y, canvas.offsetHeight - currentNode.offsetHeight)) + 'px';
            
            // Redraw connections while dragging
            drawConnections();
        }
    });
    
    document.addEventListener('mouseup', function() {
        if (isDragging && currentNode) {
            currentNode.style.zIndex = '2';
            isDragging = false;
            currentNode = null;
        }
    });
    
    // Sidebar toggle functionality
    const sidebarHeader = document.querySelector('.sidebar-header');
    const sidebar = document.querySelector('.sidebar');
    
    sidebarHeader.addEventListener('click', function() {
        const icon = this.querySelector('i');
        if (icon.classList.contains('fa-chevron-up')) {
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
            sidebar.style.height = '60px';
            sidebar.style.overflow = 'hidden';
        } else {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
            sidebar.style.height = 'auto';
            sidebar.style.overflow = 'visible';
        }
    });
    
    // Add percentage labels to condition paths
    function addPercentageLabels() {
        const percentageLabels = [
            { text: '50%', x: 380, y: 320 },
            { text: '50%', x: 380, y: 380 },
            { text: '50%', x: 640, y: 380 }
        ];
        
        percentageLabels.forEach(label => {
            const textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            textElement.setAttribute('x', label.x);
            textElement.setAttribute('y', label.y);
            textElement.setAttribute('fill', '#28a745');
            textElement.setAttribute('font-size', '12');
            textElement.setAttribute('font-weight', 'bold');
            textElement.setAttribute('text-anchor', 'middle');
            textElement.textContent = label.text;
            
            svg.appendChild(textElement);
        });
    }
    
    // Add percentage labels after drawing connections
    setTimeout(() => {
        addPercentageLabels();
    }, 200);
});
